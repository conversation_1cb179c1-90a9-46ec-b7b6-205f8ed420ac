package com.guigu.service;

import com.guigu.entity.Goods;

import java.util.List;

/**
 * 商品服务接口
 */
public interface GoodsService {
    
    /**
     * 根据供应商ID获取商品列表
     * @param providerId 供应商ID
     * @return 商品列表
     */
    List<Goods> getGoodsByProviderId(Integer providerId);
    
    /**
     * 获取所有可用的商品列表
     * @return 商品列表
     */
    List<Goods> getAvailableGoods();
    
    /**
     * 根据ID获取商品信息
     * @param id 商品ID
     * @return 商品信息
     */
    Goods getGoodsById(Integer id);
}
