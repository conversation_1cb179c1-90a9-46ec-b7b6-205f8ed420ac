package com.guigu.service.impl;

import com.guigu.entity.Goods;
import com.guigu.mapper.GoodsMapper;
import com.guigu.service.GoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品服务实现类
 */
@Service
public class GoodsServiceImpl implements GoodsService {
    
    @Autowired
    private GoodsMapper goodsMapper;
    
    @Override
    public List<Goods> getGoodsByProviderId(Integer providerId) {
        if (providerId == null) {
            return getAvailableGoods();
        }
        return goodsMapper.selectGoodsByProviderId(providerId);
    }
    
    @Override
    public List<Goods> getAvailableGoods() {
        return goodsMapper.selectAvailableGoods();
    }
    
    @Override
    public Goods getGoodsById(Integer id) {
        return goodsMapper.selectById(id);
    }
}
