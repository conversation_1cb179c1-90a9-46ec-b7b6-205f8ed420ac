package com.guigu.controller;

import com.guigu.common.Result;
import com.guigu.entity.Goods;
import com.guigu.service.GoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品控制器
 */
@RestController
@RequestMapping("/goods")
@CrossOrigin
public class GoodsController {
    
    @Autowired
    private GoodsService goodsService;
    
    /**
     * 获取商品列表
     * @param providerId 供应商ID（可选）
     * @return 商品列表
     */
    @GetMapping("/list")
    public Result<List<Goods>> getGoodsList(@RequestParam(required = false) Integer providerId) {
        try {
            List<Goods> goods = goodsService.getGoodsByProviderId(providerId);
            return Result.success(goods);
        } catch (Exception e) {
            return Result.error("获取商品列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取商品信息
     * @param id 商品ID
     * @return 商品信息
     */
    @GetMapping("/{id}")
    public Result<Goods> getGoodsById(@PathVariable Integer id) {
        try {
            Goods goods = goodsService.getGoodsById(id);
            if (goods != null) {
                return Result.success(goods);
            } else {
                return Result.error(404, "商品不存在");
            }
        } catch (Exception e) {
            return Result.error("获取商品信息失败：" + e.getMessage());
        }
    }
}
