package com.guigu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guigu.entity.Goods;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商品Mapper接口
 */
@Mapper
public interface GoodsMapper extends BaseMapper<Goods> {
    
    /**
     * 根据供应商ID查询商品列表
     * @param providerId 供应商ID
     * @return 商品列表
     */
    @Select("SELECT * FROM bus_goods WHERE providerid = #{providerId} AND available = 1 ORDER BY id")
    List<Goods> selectGoodsByProviderId(@Param("providerId") Integer providerId);
    
    /**
     * 查询所有可用的商品
     * @return 商品列表
     */
    @Select("SELECT * FROM bus_goods WHERE available = 1 ORDER BY id")
    List<Goods> selectAvailableGoods();
}
