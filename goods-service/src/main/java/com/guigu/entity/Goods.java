package com.guigu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 商品实体类
 * 对应数据库表：bus_goods
 */
@Data
@TableName("bus_goods")
public class Goods {
    
    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 商品名称
     */
    private String goodsname;
    
    /**
     * 供应商ID（外键）
     */
    private Integer providerid;
    
    /**
     * 产地
     */
    private String produceplace;
    
    /**
     * 规格
     */
    private String size;
    
    /**
     * 包装
     */
    private String goodspackage;
    
    /**
     * 产品编码
     */
    private String productcode;
    
    /**
     * 承诺编码
     */
    private String promitcode;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 价格
     */
    private Double price;
    
    /**
     * 库存数量
     */
    private Integer number;
    
    /**
     * 警戒数量
     */
    private Integer dangernum;
    
    /**
     * 商品图片
     */
    private String goodsimg;
    
    /**
     * 是否可用（1：可用，0：不可用）
     */
    private Integer available;
}
