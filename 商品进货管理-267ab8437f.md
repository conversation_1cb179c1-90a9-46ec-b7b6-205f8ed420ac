# 商品进货管理

# 数据库

数据库使用mysql数据库，将参考资料中的sql脚本导入到数据库中。

创建数据库warehouse_provider，包含供货商表（bus_provider）


创建数据库warehouse_goods，包含商品表（bus_goods）

创建数据库warehouse_inport，包含进货单表（bus_inport）


## 数据库结构详细说明（根据SQL文件修正）

### 数据库warehouse_provider下的供货商表（bus_provider）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键，自增 |
| providername | varchar(255) | 供应商名称 |
| zip | varchar(255) | 邮编 |
| address | varchar(255) | 地址 |
| telephone | varchar(255) | 电话 |
| connectionperson | varchar(255) | 联系人 |
| phone | varchar(255) | 手机 |
| bank | varchar(255) | 银行 |
| account | varchar(255) | 账号 |
| email | varchar(255) | 邮箱 |
| fax | varchar(255) | 传真 |
| available | int | 是否可用 |

### 数据库warehouse_goods下的商品表（bus_goods）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键，自增 |
| goodsname | varchar(255) | 商品名称 |
| providerid | int | 供应商ID（外键） |
| produceplace | varchar(255) | 产地 |
| size | varchar(255) | 规格 |
| goodspackage | varchar(255) | 包装 |
| productcode | varchar(255) | 产品编码 |
| promitcode | varchar(255) | 承诺编码 |
| description | varchar(255) | 描述 |
| price | double | 价格 |
| number | int | 库存数量 |
| dangernum | int | 警戒数量 |
| goodsimg | varchar(255) | 商品图片 |
| available | int | 是否可用 |

### 数据库warehouse_inport下的进货单表（bus_inport）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键，自增 |
| paytype | varchar(255) | 支付方式 |
| inporttime | datetime | 进货时间 |
| operateperson | varchar(255) | 操作人 |
| number | int | 进货数量 |
| remark | varchar(255) | 备注 |
| inportprice | double | 进货价格 |
| providerid | int | 供应商ID（外键） |
| goodsid | int | 商品ID（外键） |

# 工程搭建及所选技术栈要求

要求前后端分离开发

后端工程使用Maven、SpringBoot、Mybatis Plus、SpringCloud框架整合完成。要求使用父子工程和聚合工程形式搭建项目。

前端工程要求采用Vue3、ElementPlus实现单文件组件化开发。

前端工程访问后端工程必须通过gateway网关实现。

# 界面一：进货单查询页面

要求：

（1）搜索条件中的"供应商名称"应从数据库中查询得出。

（2）搜索条件中的"商品名称"应从数据库中查询得出，并且需要根据所选的"供应商名称"进行变化显示。

（3）完成根据条件查询的分页显示功能，如上图所示。

（4）"操作员"一列可忽略。

# 界面二：添加进货单

要求：

点击"添加进货"按钮，弹出"添加进货"窗口，录入相关信息，点击"提交"实现添加进货单业务。

"供应商名称"应从数据库中查询得出。

"商品名称"应从数据库中查询得出，并且需要根据所选的"供应商名称"进行变化显示。

# 界面三：修改进货单功能

要求：

（1）点击"编辑"按钮，弹出"修改商品进货"窗口，显示相关信息，点击"提交"实现修改进货单业务。

（2）"供应商名称"应从数据库中查询得出。

（3）"商品名称"应从数据库中查询得出，并且需要根据所选的"供应商名称"进行变化显示。


## 后端API接口规范

### 1. 供应商管理
- **获取供应商列表**
  - URL: `/api/provider/list`
  - Method: GET
  - Response: `{ code: 200, data: [{ id: 1, providername: '供应商A' }] }`

### 2. 商品管理
- **根据供应商ID获取商品列表**
  - URL: `/api/goods/list?providerId=1`
  - Method: GET
  - Response: `{ code: 200, data: [{ id: 101, goodsname: '商品A' }] }`

### 3. 进货单管理
- **分页查询进货单**
  - URL: `/api/inport/list?pageNum=1&pageSize=10&providerId=1&goodsId=101`
  - Method: GET
  - Response: `{ code: 200, data: { list: [...], total: 100 } }`

- **获取进货单详情**
  - URL: `/api/inport/{id}`
  - Method: GET
  - Response: `{ code: 200, data: { ... } }`

- **添加进货单**
  - URL: `/api/inport`
  - Method: POST
  - Request Body: 
    ```json
    {
      "paytype": "银联",
      "inporttime": "2025-09-22 15:30:05",
      "operateperson": "admin",
      "number": 100,
      "remark": "",
      "inportprice": 10.5,
      "providerid": 1,
      "goodsid": 101
    }
    ```

- **修改进货单**
  - URL: `/api/inport/{id}`
  - Method: PUT
  - Request Body: 同上

- **删除进货单**
  - URL: `/api/inport/{id}`
  - Method: DELETE

## 图片

![图片](images/image1.png)


![图片](images/image2.png)



![图片](images/image3.png)



![图片](images/image4.png)



![图片](images/image5.png)



![图片](images/image6.png)



![图片](images/image7.png)

