# ===================================================================
# 网关服务核心配置
# ===================================================================

server:
  # 网关服务端口
  # 网关作为所有服务的统一入口，监听8080端口
  port: 8080

spring:
  # 应用名称
  # 在微服务架构中，应用名称用于服务注册与发现
  application:
    name: gateway-service

  # 云端配置（Spring Cloud相关）
  cloud:
    # 服务发现配置
    # Nacos服务发现配置
    # 用于将网关服务注册到Nacos，并从Nacos获取其他服务的信息
    nacos:
      discovery:
        # Nacos服务器地址
        # 网关将通过此地址连接到Nacos服务器进行服务注册与发现
        server-addr: localhost:8848

    # Spring Cloud Gateway配置
    # 网关的核心配置，包括路由规则、过滤器等
    gateway:
      # 路由配置
      # 定义请求如何路由到后端服务
      routes:
        # 供应商服务路由
        # 处理以/api/provider/开头的请求，转发到provider-service服务
        - id: provider-service
          uri: lb://provider-service
          predicates:
            - Path=/api/provider/**
          filters:
            - StripPrefix=1

        # 商品服务路由
        # 处理以/api/goods/开头的请求，转发到goods-service服务
        - id: goods-service
          uri: lb://goods-service
          predicates:
            - Path=/api/goods/**
          filters:
            - StripPrefix=1

        # 进货单服务路由
        # 处理以/api/inport/开头的请求，转发到inport-service服务
        - id: inport-service
          uri: lb://inport-service
          predicates:
            - Path=/api/inport/**
          filters:
            - StripPrefix=1

      # 全局过滤器配置
      # 应用于所有路由的过滤器
      default-filters:
        # 添加CORS过滤器
        # DedupeResponseHeader用于去除重复的响应头
        # 去除重复的Access-Control-Allow-Credentials和Access-Control-Allow-Origin响应头
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin

      # 全局CORS配置
      # 配置跨域资源共享（CORS）规则
      globalcors:
        cors-configurations:
          '[/**]':
            # 允许的源域名
            allowedOrigins: "*"
            # 允许的请求方法
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            # 允许的请求头
            allowedHeaders: "*"
            # 暴露给浏览器的响应头
            exposedHeaders:
              - Authorization
              - Content-Disposition
              - X-Total-Count

# 日志配置
# 配置日志输出级别，便于调试和监控
logging:
  level:
    # 设置网关相关日志级别为info
    org.springframework.cloud.gateway: info
    org.springframework.cloud.loadbalancer: info
