package com.guigu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 进货单实体类
 * 对应数据库表：bus_inport
 */
@Data
@TableName("bus_inport")
public class Inport {
    
    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 支付方式
     */
    private String paytype;
    
    /**
     * 进货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inporttime;
    
    /**
     * 操作人
     */
    private String operateperson;
    
    /**
     * 进货数量
     */
    private Integer number;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 进货价格
     */
    private Double inportprice;
    
    /**
     * 供应商ID（外键）
     */
    private Integer providerid;
    
    /**
     * 商品ID（外键）
     */
    private Integer goodsid;
}
