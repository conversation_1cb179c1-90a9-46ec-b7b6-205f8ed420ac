package com.guigu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guigu.entity.Inport;
import com.guigu.vo.InportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 进货单Mapper接口
 */
@Mapper
public interface InportMapper extends BaseMapper<Inport> {
    
    /**
     * 分页查询进货单（包含供应商和商品名称）
     * @param page 分页对象
     * @param providerId 供应商ID（可选）
     * @param goodsId 商品ID（可选）
     * @return 进货单列表
     */
    @Select("<script>" +
            "SELECT i.*, p.providername, g.goodsname " +
            "FROM bus_inport i " +
            "LEFT JOIN warehouse_provider.bus_provider p ON i.providerid = p.id " +
            "LEFT JOIN warehouse_goods.bus_goods g ON i.goodsid = g.id " +
            "WHERE 1=1 " +
            "<if test='providerId != null'> AND i.providerid = #{providerId} </if>" +
            "<if test='goodsId != null'> AND i.goodsid = #{goodsId} </if>" +
            "ORDER BY i.inporttime DESC" +
            "</script>")
    Page<InportVO> selectInportPage(Page<InportVO> page, 
                                   @Param("providerId") Integer providerId, 
                                   @Param("goodsId") Integer goodsId);
    
    /**
     * 根据ID查询进货单详情（包含供应商和商品名称）
     * @param id 进货单ID
     * @return 进货单详情
     */
    @Select("SELECT i.*, p.providername, g.goodsname " +
            "FROM bus_inport i " +
            "LEFT JOIN warehouse_provider.bus_provider p ON i.providerid = p.id " +
            "LEFT JOIN warehouse_goods.bus_goods g ON i.goodsid = g.id " +
            "WHERE i.id = #{id}")
    InportVO selectInportVOById(@Param("id") Integer id);
}
