package com.guigu.controller;

import com.guigu.common.PageResult;
import com.guigu.common.Result;
import com.guigu.entity.Inport;
import com.guigu.service.InportService;
import com.guigu.vo.InportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 进货单控制器
 */
@RestController
@RequestMapping("/inport")
@CrossOrigin
public class InportController {
    
    @Autowired
    private InportService inportService;
    
    /**
     * 分页查询进货单
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param providerId 供应商ID（可选）
     * @param goodsId 商品ID（可选）
     * @return 分页结果
     */
    @GetMapping("/list")
    public Result<PageResult<InportVO>> getInportList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer providerId,
            @RequestParam(required = false) Integer goodsId) {
        try {
            PageResult<InportVO> result = inportService.getInportPage(pageNum, pageSize, providerId, goodsId);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询进货单列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取进货单详情
     * @param id 进货单ID
     * @return 进货单详情
     */
    @GetMapping("/{id}")
    public Result<InportVO> getInportById(@PathVariable Integer id) {
        try {
            InportVO inport = inportService.getInportById(id);
            if (inport != null) {
                return Result.success(inport);
            } else {
                return Result.error(404, "进货单不存在");
            }
        } catch (Exception e) {
            return Result.error("获取进货单详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 添加进货单
     * @param inport 进货单信息
     * @return 操作结果
     */
    @PostMapping
    public Result<Void> addInport(@RequestBody Inport inport) {
        try {
            boolean success = inportService.addInport(inport);
            if (success) {
                return Result.success();
            } else {
                return Result.error("添加进货单失败");
            }
        } catch (Exception e) {
            return Result.error("添加进货单失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改进货单
     * @param id 进货单ID
     * @param inport 进货单信息
     * @return 操作结果
     */
    @PutMapping("/{id}")
    public Result<Void> updateInport(@PathVariable Integer id, @RequestBody Inport inport) {
        try {
            inport.setId(id);
            boolean success = inportService.updateInport(inport);
            if (success) {
                return Result.success();
            } else {
                return Result.error("修改进货单失败");
            }
        } catch (Exception e) {
            return Result.error("修改进货单失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除进货单
     * @param id 进货单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteInport(@PathVariable Integer id) {
        try {
            boolean success = inportService.deleteInport(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除进货单失败");
            }
        } catch (Exception e) {
            return Result.error("删除进货单失败：" + e.getMessage());
        }
    }
}
