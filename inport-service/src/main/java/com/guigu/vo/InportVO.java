package com.guigu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 进货单视图对象
 * 包含供应商名称和商品名称
 */
@Data
public class InportVO {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 支付方式
     */
    private String paytype;
    
    /**
     * 进货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inporttime;
    
    /**
     * 操作人
     */
    private String operateperson;
    
    /**
     * 进货数量
     */
    private Integer number;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 进货价格
     */
    private Double inportprice;
    
    /**
     * 供应商ID
     */
    private Integer providerid;
    
    /**
     * 供应商名称
     */
    private String providername;
    
    /**
     * 商品ID
     */
    private Integer goodsid;
    
    /**
     * 商品名称
     */
    private String goodsname;
}
