package com.guigu.service;

import com.guigu.common.PageResult;
import com.guigu.entity.Inport;
import com.guigu.vo.InportVO;

/**
 * 进货单服务接口
 */
public interface InportService {
    
    /**
     * 分页查询进货单
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param providerId 供应商ID（可选）
     * @param goodsId 商品ID（可选）
     * @return 分页结果
     */
    PageResult<InportVO> getInportPage(Integer pageNum, Integer pageSize, Integer providerId, Integer goodsId);
    
    /**
     * 根据ID获取进货单详情
     * @param id 进货单ID
     * @return 进货单详情
     */
    InportVO getInportById(Integer id);
    
    /**
     * 添加进货单
     * @param inport 进货单信息
     * @return 是否成功
     */
    boolean addInport(Inport inport);
    
    /**
     * 修改进货单
     * @param inport 进货单信息
     * @return 是否成功
     */
    boolean updateInport(Inport inport);
    
    /**
     * 删除进货单
     * @param id 进货单ID
     * @return 是否成功
     */
    boolean deleteInport(Integer id);
}
