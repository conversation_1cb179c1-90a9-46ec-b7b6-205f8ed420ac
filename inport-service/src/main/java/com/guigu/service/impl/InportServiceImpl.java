package com.guigu.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guigu.common.PageResult;
import com.guigu.entity.Inport;
import com.guigu.mapper.InportMapper;
import com.guigu.service.InportService;
import com.guigu.vo.InportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 进货单服务实现类
 */
@Service
public class InportServiceImpl implements InportService {
    
    @Autowired
    private InportMapper inportMapper;
    
    @Override
    public PageResult<InportVO> getInportPage(Integer pageNum, Integer pageSize, Integer providerId, Integer goodsId) {
        Page<InportVO> page = new Page<>(pageNum, pageSize);
        Page<InportVO> result = inportMapper.selectInportPage(page, providerId, goodsId);
        return new PageResult<>(result.getRecords(), result.getTotal(), pageNum, pageSize);
    }
    
    @Override
    public InportVO getInportById(Integer id) {
        return inportMapper.selectInportVOById(id);
    }
    
    @Override
    public boolean addInport(Inport inport) {
        // 设置进货时间为当前时间
        if (inport.getInporttime() == null) {
            inport.setInporttime(LocalDateTime.now());
        }
        return inportMapper.insert(inport) > 0;
    }
    
    @Override
    public boolean updateInport(Inport inport) {
        return inportMapper.updateById(inport) > 0;
    }
    
    @Override
    public boolean deleteInport(Integer id) {
        return inportMapper.deleteById(id) > 0;
    }
}
