package com.guigu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guigu.entity.Provider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 供应商Mapper接口
 */
@Mapper
public interface ProviderMapper extends BaseMapper<Provider> {
    
    /**
     * 查询所有可用的供应商
     * @return 供应商列表
     */
    @Select("SELECT * FROM bus_provider WHERE available = 1 ORDER BY id")
    List<Provider> selectAvailableProviders();
}
