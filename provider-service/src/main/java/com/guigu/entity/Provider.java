package com.guigu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 供应商实体类
 * 对应数据库表：bus_provider
 */
@Data
@TableName("bus_provider")
public class Provider {
    
    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 供应商名称
     */
    private String providername;
    
    /**
     * 邮编
     */
    private String zip;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 电话
     */
    private String telephone;
    
    /**
     * 联系人
     */
    private String connectionperson;
    
    /**
     * 手机
     */
    private String phone;
    
    /**
     * 银行
     */
    private String bank;
    
    /**
     * 账号
     */
    private String account;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 传真
     */
    private String fax;
    
    /**
     * 是否可用（1：可用，0：不可用）
     */
    private Integer available;
}
