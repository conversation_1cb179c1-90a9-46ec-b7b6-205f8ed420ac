package com.guigu.controller;

import com.guigu.common.Result;
import com.guigu.entity.Provider;
import com.guigu.service.ProviderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商控制器
 */
@RestController
@RequestMapping("/provider")
@CrossOrigin
public class ProviderController {
    
    @Autowired
    private ProviderService providerService;
    
    /**
     * 获取供应商列表
     * @return 供应商列表
     */
    @GetMapping("/list")
    public Result<List<Provider>> getProviderList() {
        try {
            List<Provider> providers = providerService.getAvailableProviders();
            return Result.success(providers);
        } catch (Exception e) {
            return Result.error("获取供应商列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取供应商信息
     * @param id 供应商ID
     * @return 供应商信息
     */
    @GetMapping("/{id}")
    public Result<Provider> getProviderById(@PathVariable Integer id) {
        try {
            Provider provider = providerService.getProviderById(id);
            if (provider != null) {
                return Result.success(provider);
            } else {
                return Result.error(404, "供应商不存在");
            }
        } catch (Exception e) {
            return Result.error("获取供应商信息失败：" + e.getMessage());
        }
    }
}
