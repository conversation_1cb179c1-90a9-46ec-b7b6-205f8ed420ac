package com.guigu.service.impl;

import com.guigu.entity.Provider;
import com.guigu.mapper.ProviderMapper;
import com.guigu.service.ProviderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商服务实现类
 */
@Service
public class ProviderServiceImpl implements ProviderService {
    
    @Autowired
    private ProviderMapper providerMapper;
    
    @Override
    public List<Provider> getAvailableProviders() {
        return providerMapper.selectAvailableProviders();
    }
    
    @Override
    public Provider getProviderById(Integer id) {
        return providerMapper.selectById(id);
    }
}
