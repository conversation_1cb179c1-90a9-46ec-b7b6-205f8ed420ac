server:
  port: 7777
spring:
  application:
    name: provider-service
  cloud:
    #    nacos 配置
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  #        8版本 mysql 配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
#        mybatis-plus 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.guigu.springcloud.domain.entity
  configuration:
    lazy-loading-enabled: true # 延迟加载
    aggressive-lazy-loading: false # 积极的延迟加载
    cache-enabled: true # 缓存
feign:
  #    feign-okhttp 配置 调用速度快，占用内存少，异步请求
  okhttp:
    enabled: true
  #    feign-httpclient 配置 调用速度慢，占用内存大，功能多
  httpclient:
    enabled: false    # 切换底层实现为httpclient

